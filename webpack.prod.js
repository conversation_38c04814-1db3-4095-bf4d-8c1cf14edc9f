const { merge } = require("webpack-merge");
const common = require("./webpack.common.js");
const utils = require("./webpack.utils.js");

/* plugins */
const ZipPlugin = require("zip-webpack-plugin");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const TerserPlugin = require('terser-webpack-plugin');


module.exports = (env) => {
	return merge(common(env), {
		mode: "production",
		plugins: [
			/*new ZipPlugin({
				path: "../release",
				filename: utils.envInfo.archive
			}),*/
			new CopyWebpackPlugin(
				{
					patterns:
						[
							{
								context: "src/",
								from: "ascData.json",
								to: utils.envInfo.outputPath,
								transform(content)
								{
									return utils.setPlaceholder(
										content.toString(),
										{
											SPOT_IDENTIFIER: utils.pkg.name,
											VERSION: utils.pkg.version,
										},
										"@@"
									)
								}
							}
						]
				}
			)
		],
		optimization: {
			minimizer: [new TerserPlugin({
				extractComments: false,
			})],
		},
	});
};
