const webpack = require("webpack");
const utils = require("./webpack.utils.js");

/* plugins */
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const ZipPlugin = require("zip-webpack-plugin");
const noop = require('noop-webpack-plugin')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

module.exports = (env, argv) =>
{
	utils.setEnvironment(env, argv);

  let entry = {};
  let gfLibs= ['./external/grassfish/libs/index.js'];

  if (utils.envInfo.nodeEnv === 'dev')
  {
    entry = {
      gf: gfLibs,
      app: {
        import: './src/index.dev.ts',
        dependOn: ['gf', 'mock']
      },
      mock: {
        import: './src/mock/browser.ts',
        dependOn: ['gf']
      }
    };
  } else {
    entry = {
      gf: gfLibs,
      app: {
        import: './src/index.ts',
        dependOn: ['gf']
      }
    };
  }

	return {
		entry,
		module: {
			rules: [
				// scripts
				{
					test: /\.(ts|js)x?$/,
					exclude: /node_modules/,
					use: {
						loader: "babel-loader"
					}
				},

				// styles
				{
					test: /\.(scss|css)$/,
					use: ["style-loader", "css-loader", "sass-loader"],
				},

        // images
        {
          test: /\.(png|jpe?g|gif|webp|svg)$/i,
          type: 'asset/resource',
          generator: {
            filename: 'assets/img/[name][ext]',
          },
        },

        // fonts
				{
					test: /\.(woff|woff2|eot|ttf|otf)$/,
					type: 'asset/resource',
					generator: {
						filename: 'assets/fonts/[hash][ext]',
					}
				},

				// templates
				{
					test: /\.(tmpl.html)$/,
					use: [
						{
							loader: "mustache-loader?noShortcut",
						},
					],
				},
			],
		},
		output: {
			filename: "[name].bundle.js",
			path: utils.envInfo.outputPath,
		},
		plugins: [
			new webpack.EnvironmentPlugin({
				//NODE_ENV: utils.envInfo.nodeEnv,
				SPOT_NAME: utils.pkg.name,
			}),
			new CleanWebpackPlugin(), //[utils.envInfo.outputDir]),
			new HtmlWebpackPlugin({
				title: utils.pkg.name,
				chunks: Object.keys(entry),
				chunksSortMode: "auto",
				meta: {
					viewport: "width=device-width, initial-scale=1",
				},
			}),
			env.zip ? new ZipPlugin({
				path: "../release",
				filename: utils.envInfo.archive
			}) : noop(),

      // Generate bundle size report at ./dist/report.html
      new BundleAnalyzerPlugin({
        analyzerMode: "static",
        openAnalyzer: false,
      })
		],

		// support imports
		resolve: {
			extensions: [".js", ".jsx", ".ts", ".tsx", ".scss"],
		},
	};
};
