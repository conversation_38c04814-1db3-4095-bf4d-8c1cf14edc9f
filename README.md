## Description
This project serves as a draft for generic spot templates.
It includes sample calls to different types of player, udc and external datasources.

## Node Version
`node 16.17.0`

## Prerequisites
* TypeScript
* Sass

## Instructions

In order to start this project, make sure to run `npm install` at first startup.

* Development
  * Command: `npm start`
    * Starts the application in localhost
* Build
  * Command: `npm run build.dev`
    * Builds the application with the development environment
  * Command: `npm run build.prod`
    * Builds the application with the production environment
* Release
    * Command: `npm run release.dev`
      * Builds the application with the development environment and zips its contents. Zip name will be as specified in package.json (name + version)
    * Command: `npm run release.prod`
      * Builds the application with the production environment and zips its contents. Zip name will be as specified in package.json (name + version)

## Mocking

In order to mock your API, go to `mock/__stubs__/common` and add your folder containing the resolver and the mocked data.
You can look at  `mock/__stubs__/common/example`
Finally, go to `mock/handlers.ts` and your handler function to the `handlers` array

## Todo list
 
- [ ] Check for full browser compatibility 
  - (Chrome, Firefox, Edge, Safari iOS latest 2 Versionen)
- [ ] Necessary Babel-configuration adjustments
- [ ] Code-Beautifier for other IDEs other than WebStorm
- [ ] Eslint, .editor
- [ ] .nvmrc (v0.0.0), .npmrc (engine-strict=true) (Windows Bug)
- [ ] exclude ascInterface (gfSpotBase, gfTizenBase, gfUdcConnector, gfWizardbase etc.) from git
- [ ] .gitkeep (file .gitkeep in folders that get pushed)
- [ ] complete prerequisites section in readme
- [ ] complete documentation section in readme
- [ ] generic mock file for development
- [ ] loading mock media files with correct paths
- [ ] proper split between generic and spot components
- [ ] improve and refactor util functions for generic and spot specific
