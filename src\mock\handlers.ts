import { rest } from 'msw';
import { udcResponseResolver } from './__stubs__/core/udc/udc.resolver';
import { masterDetailQtResolver, masterDetailServerResolver } from './__stubs__/core/master-detail/master-detail.resolver';
import { additionalDataServerResolver } from './__stubs__/core/additional-data/additional-data.resolver';
import { mediaResolver } from './__stubs__/core/media/media.resolver';

export const handlers: any = [
  rest.get('**/DataSource/*', udcResponseResolver),
  rest.get(`**/Players/*`, masterDetailServerResolver),
  rest.get(`**/*/AdditionalData`, additionalDataServerResolver),
  rest.get('**/MasterDetails', masterDetailQtResolver),
  rest.get('**/media/*', mediaResolver),
]

