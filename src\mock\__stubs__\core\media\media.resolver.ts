import logo from "../../../../assets/images/grassfishlogo.webp";

const imageMap: Record<string, string> = {
  'grassfishlogo.webp': logo
};

const getMimeType = (filename: string): string => {
  const ext = filename.split('.').pop()?.toLowerCase();
  switch (ext) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'webp':
      return 'image/webp';
    case 'gif':
      return 'image/gif';
    case 'svg':
      return 'image/svg+xml';
    default:
      return 'application/octet-stream';
  }
};

/*
  Media resolver when caller type is player
*/
export const mediaResolver = async (req, res, ctx) => {
  const fileName = req.params[1];

  const imageUrl = imageMap[fileName as string];

  if (!imageUrl) {
    return res(ctx.status(404), ctx.text('Image not found'));
  }

  const response = await fetch(imageUrl);
  const buffer = await response.arrayBuffer();

  return res(
    ctx.status(200),
    ctx.set('Content-Type', getMimeType(fileName as string)),
    ctx.body(buffer)
  );
};
