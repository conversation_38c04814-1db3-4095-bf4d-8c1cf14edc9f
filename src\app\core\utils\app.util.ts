export function json(value: string)
{
	let result;

	try
	{
		result = JSON.parse(value);
	}
	catch (err)
	{
	}

	return result || {};
}

export function renderDOM(template: any, data?: any, subTemplates?: any, id?: string): HTMLElement
{
	let type = template.text.startsWith("<tr") ? "table" : "div";
	let rendered: HTMLElement;
	rendered = document.createElement(type);
	rendered.innerHTML = template.render(data, subTemplates);

	// strip container(s)
	rendered = <HTMLElement>rendered.children[0]; // div,table
	if (type === "table")
	{
		rendered = <HTMLElement>rendered.children[0]; //tbody
	}

	if (id)
	{
		rendered.id = id;
	}

	return rendered;
}

export function http(method: string, url: string, data?: any, header?: any): Promise<any>
{
		return new Promise((resolve, reject) =>
	{
		const request = new XMLHttpRequest();
		request.open(method, url);

		let onError = (): void =>
		{
			reject(request.statusText + "[" + request.status + "]");
		}

		// set headers
    const headers = header ?? {};
    Object.entries(headers).forEach(([name, value]) => {
      request.setRequestHeader(name, value as string);
    });

		// event handler
		request.onload = (): void => (request.status === 200 ? resolve(request.responseText) : onError());
		request.onerror = onError;

		let body = data && JSON.stringify(data)
		request.send(body);
	});
}

export function getTime(date: Date): string
{
	let result = "";

	if (date)
	{
		result = date.getHours() + ":" + date.getMinutes().toString().padStart(2, "0");
	}

	return result || "NA";
}

export function getUrl(baseUrl: string, fileName: string): string
{
	if (baseUrl && baseUrl.substr(-1) !== "/")
	{
		baseUrl += "/"
	}

	return baseUrl + fileName;
}
