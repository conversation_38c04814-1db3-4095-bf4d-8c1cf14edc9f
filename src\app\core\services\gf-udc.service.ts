import { COMMON } from "../../common.const";
import { IRawUdcData, UdcService } from "../types/udc.type";
import { AscDataService } from "./asc-data.service";

/* GfUdcConnectorService Singleton
 * -> preparation for later implementation of wrapper class for GF Library
 */
class GfUdcServiceSingleton implements UdcService
{
	private _udcConnector: any = {};

	constructor()
	{
		if (window.gfUdcConnector)
		{
			this._udcConnector = window.gfUdcConnector;
		} else {
			throw "NoUdcConnectorException";
		}
	}

	load(key: string): Promise<IRawUdcData>
	{
		return new Promise((resolve, reject) =>
		{
			this._udcConnector.getDataByKey(key, resolve, reject);
		});
	}

	loadUdcElements()
	{
		return this.load(AscDataService.value(COMMON.CONFIG.DATA.DATA_SOURCE) as string || "")
		.then((response: IRawUdcData) => response);
	}
}

export const GfUdcService = new GfUdcServiceSingleton();
