{"name": "@grassfish/gf-htmlwizardspots-template-basic", "version": "1.0.0", "description": "Generic Grassfish spot template.", "private": true, "scripts": {"start": "webpack-dev-server --config webpack.dev.js --node-env dev", "build.dev": "webpack --config webpack.dev.js --node-env dev", "build.prod": "webpack --config webpack.prod.js --node-env prod", "build.ci": "npm install-ci-test && npm run build.prod", "release.dev": "webpack --config webpack.dev.js --node-env dev --env zip", "release.prod": "webpack --config webpack.prod.js --node-env prod  --env zip", "test": "echo -e \"🐟 Running project tests\" && npx jest && exit 0", "lint": "npx eslint --ignore-path .prettierignore ./src", "lint-fix": "npx eslint --fix --ignore-path .prettierignore ./src", "format": "npx prettier --ignore-path .prettierignore --write .", "preversion": "npm install-ci-test", "version": "npm run build.ci && git add package.json package-lock.json", "postversion": "git push && git push --tags", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+ssh://bitbucket.org:grassfishbucket/gf-htmlwizardspots-template-basic.git"}, "publishConfig": {"registry": "https://grassfish.jfrog.io/artifactory/api/npm/frontend-npm-virtual/"}, "files": ["./dist"], "keywords": ["Grassfish", "Template", "HtmlWizardSpot"], "author": "Grassfish Marketing Technologies GmbH <<EMAIL>> (https://grassfish.com)", "license": "UNLICENSED", "homepage": "https://bitbucket.org/grassfishbucket/gf-htmlwizardspots-template-basic#readme", "engines": {"node": "18.16.1"}, "browserslist": ["chrome >= 80", "last 2 versions", "not dead", "> 0.2%"], "devDependencies": {"@babel/core": "^7.22.6", "@babel/preset-env": "^7.22.6", "@babel/preset-typescript": "^7.22.5", "@jest/globals": "^29.7.0", "@types/hogan.js": "^3.0.5", "@types/lodash": "^4.14.195", "@types/mustache": "^4.2.2", "@types/node": "^20.4.0", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "babel-loader": "^9.1.2", "babel-plugin-transform-class-properties": "^6.24.1", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "eslint": "^8.41.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "hogan.js": "^3.0.2", "html-webpack-plugin": "^5.5.3", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-html-loader": "^1.0.0", "jest-transform-stub": "^2.0.0", "msw": "1.3.2", "mustache-loader": "^1.4.3", "node-sass": "^9.0.0", "noop-webpack-plugin": "^1.0.1", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "sass-loader": "^13.3.2", "script-loader": "^0.7.2", "style-loader": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "ts-jest": "^29.2.5", "typescript": "^5.1.6", "webpack": "^5.88.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-log": "^3.0.2", "webpack-merge": "^5.9.0", "zip-webpack-plugin": "^4.0.1"}, "dependencies": {"core-js": "^3.31.1", "gfspotbase": "file:./external/grassfish/libs/gfSpotBase", "gftizenbase": "file:./external/grassfish/libs/gfTizenBase", "gfudcconnector": "file:./external/grassfish/libs/gfUdcConnector", "gfwizardbase": "file:./external/grassfish/libs/gfWizardBase"}, "msw": {"workerDirectory": "src/mock"}}