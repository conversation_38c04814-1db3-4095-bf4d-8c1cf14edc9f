import { EnvironmentService } from "./environment.service";
import { GfSpotBaseService } from "./gf-spot-base.service";
import { LoggerService } from "./logger.service";
import { CORE } from "../core.const";
import { GfWizardBaseService } from "./gf-wizard-base.service";
import { IAppService } from "../types/app.type";
import { IEnvironment } from "../types/environment.type";
import { IMasterDetail, IMasterDetailCms } from "../types/master-detail.type";
import { http, json } from "../utils/app.util";
import { masterDetailsFactory } from "../factories/master-details.factory";
import { GfTizenBaseService } from "./gf-tizen-base.service";

class AppServiceSingleton implements IAppService
{
	private _env: IEnvironment
	private _callerType: string;
	private _masterDetail: IMasterDetail;

	constructor()
	{
		this._env = EnvironmentService.getEnvironment();
	}

	getCallerType(): string
	{
		if (!this._callerType)
		{
			this._callerType = GfSpotBaseService.getCallerType();
		}
		LoggerService.debug("caller type: " + this._callerType);

		return this._callerType;
	}

	get masterDetail(): IMasterDetail
	{
		return this._masterDetail;
	}

	set masterDetail(masterDetail: IMasterDetail)
	{
		this._masterDetail = masterDetail;
	}

	loadMasterDetailFromServer(locationOrigin: string, locationId: string, sessionId: string): Promise<IMasterDetail>
	{
		let promise;

		LoggerService.debug("AppService.loadMasterDetailFromServer: called.");

		if ((this._env.mock && this._env.mock.masterDetail) || locationId)
		{
			let header = { "X-Session-Id": sessionId };
			let url = locationOrigin + "/gv2/webservices/API/v1.3/Players/" + locationId;
			promise = Promise.all([
				http(CORE.METHOD.GET, url, undefined, header),
        http(CORE.METHOD.GET, url + "/AdditionalData", undefined, header)
			])
				.then((results) =>
				{
					let masterDetail = <IMasterDetailCms>JSON.parse(results[0]);
					masterDetail.AdditionalProperties = JSON.parse(results[1]);
					return masterDetail;
				});
		}
		else
		{
			LoggerService.error("AppService.loadMasterDetailFromServer: no location ID found.");
			promise = Promise.reject("MISSING_LOCATION_ID");
		}

		return promise;
	}

	loadMasterDetail(): Promise<IMasterDetail>
	{
		let promise;

		LoggerService.debug("AppService.loadMasterDetail: called.");

		// server
		if (this._env.mock && this._env.mock.context === CORE.CONTEXT.SERVER ||
			this.getCallerType() === CORE.CALLER_TYPE.ASC_EDITOR ||
			this.getCallerType() === CORE.CALLER_TYPE.PREVIEW)
		{
			promise = this.loadMasterDetailFromServer(window.location.origin, GfSpotBaseService.findUrlParam("locationId"), GfWizardBaseService.getSessionId())
				.then((result) => masterDetailsFactory(result));
		}
		// player
		else
		{
			if ((this._env.mock && this._env.mock.context === CORE.CONTEXT.TIZEN_PLAYER) ||
				this.getCallerType() === CORE.CALLER_TYPE.TIZEN_PLAYER)
			{
				promise = (GfTizenBaseService.loadMasterDetailTizen() || GfSpotBaseService.readFile("../../backup/", "box.json", 2000))
					.then((content) => json(content));
			}
			// qt player
			else
			{
				promise = http(CORE.METHOD.GET, "http://localhost:8080/REST/PlayerDetails/MasterDetails")
					.then((result) => masterDetailsFactory(json(result)));
			}
		}

		return promise;
	}
}

export const AppService = new AppServiceSingleton();
