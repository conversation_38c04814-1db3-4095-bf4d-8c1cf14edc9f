import { COMMON } from "../../common.const";
import { AppService } from "../../core/services/app.service";
import { renderDOM } from "../../core/utils/app.util";
import { ascDataFactory } from "../../core/factories/asc-data.factory";
import { GfUdcService } from "../../core/services/gf-udc.service";
import { IRawUdcData } from "../../core/types/udc.type";
import { udcDataFactory } from "../../core/factories/udc.factory";
// @ts-ignore
import spotTmpl from "./spot.tmpl.html";
import "./spot.scss";
import * as Mustache from 'mustache';

type SaWiEvent = {
  id: string;
  title: string;
  startIso: string;            // ISO
  dateLabel: string;           // "DD.MM.YYYY, HH:MM Uhr"
  imageUrl: string;
  ticketUrl: string;           // QR target
  ticketsRemaining: number | null; // null = unknown => available
  soldOut: boolean;
};

export class SpotComponent
{
	
  async render(): Promise<HTMLElement> {
    const masterDetail = AppService.masterDetail || (await AppService.loadMasterDetail());
    AppService.masterDetail = masterDetail;

    // ---------- CONFIG coming from MasterDetail (with defaults) ----------
    const mode: 'list' | 'details' = (masterDetail?.mode || 'list').toLowerCase();
    const orientation: 'landscape' | 'portrait' = (masterDetail?.orientation || 'landscape').toLowerCase();
    const website = masterDetail?.website || 'www.sportarenawien.at';
    const pageSize = 4;
    const maxPages = 2;
    const rotateSeconds = Number(masterDetail?.rotateSeconds || 8);
    const qrToggleSeconds = Number(masterDetail?.qrToggleSeconds || 6);

    // ---------- LOAD UDC ----------
    const udc = await this.loadUdc('WienTicketEvents'); // ⚠️ set your UDC name here
    const events = this.mapUdcToEvents(udc);
    // Sort chronologically
    events.sort((a, b) => new Date(a.startIso).getTime() - new Date(b.startIso).getTime());

    // Decide which events to show
    const listPages = this.paginate(events, pageSize, maxPages);
    const detailsItems = events.slice(0, 2);

    const dataForView = {
      orientation,
      isLandscape: orientation === 'landscape',
      isPortrait: orientation === 'portrait',
      isList: mode === 'list',
      isDetails: mode === 'details',
      website,
      // We render empty containers; JS will inject cards
    };

    // ---------- Render Mustache ----------
    const rendered = Mustache.render(spotTmpl as unknown as string, dataForView);
    const parsed = JSON.parse(rendered);
    const root = document.createElement('div');
    root.innerHTML = parsed.text;

    // ---------- Hydrate content ----------
    if (mode === 'list') {
      this.renderList(root, listPages, rotateSeconds, qrToggleSeconds);
    } else {
      this.renderDetails(root, detailsItems, qrToggleSeconds);
    }

    return root;
  }

  // ---- UDC loader ----
  private async loadUdc(udcName: string): Promise<IRawUdcData> {
    try {
      const udcData = await GfUdcService.load(udcName);
      if (!udcData?.Success || !udcData?.Result?.Elements) {
        throw new Error('Invalid UDC payload');
      }
      return udcData;
    } catch (err) {
      console.error('UDC load failed:', err);
      return { Result: { Elements: [] } } as unknown as IRawUdcData;
    }
  }

  // ---- Mapping UDC → SaWiEvent ----
  private mapUdcToEvents(udc: IRawUdcData): SaWiEvent[] {
    // Each element.Result.Elements[i].Values.<Field>
    const els: any[] = udc?.Result?.Elements || [];
    return els.map((el: any) => {
      const v = el?.Values || el?.ElementValues || el || {};
      const start = v.StartDateTime || v.startDateTime || v.startIso;
      const d = start ? new Date(start) : null;
      return {
        id: String(v['External ID'] || v.ExternalID || el?.Id || Math.random()),
        title: v.Title || v.Name || '—',
        startIso: d ? d.toISOString() : '',
        dateLabel: d ? this.toGermanDate(d) : '00.00.2025, 00:00 Uhr',
        imageUrl: v.ImageUrl || v.ImageURL || '',
        ticketUrl: v.DetailUrl || v.Url || '',
        ticketsRemaining: this.parseFreeCount(v.FreeCount),
        soldOut: Number(v.FreeCount) === 0
      };
    }).filter(e => e.startIso);
  }

  private parseFreeCount(x: any): number | null {
    if (x === null || x === undefined || x === '' || isNaN(Number(x))) return null;
    return Number(x);
  }

  private toGermanDate(d: Date): string {
    const dd = String(d.getDate()).padStart(2, '0');
    const mm = String(d.getMonth() + 1).padStart(2, '0');
    const yyyy = d.getFullYear();
    const hh = String(d.getHours()).padStart(2, '0');
    const mi = String(d.getMinutes()).padStart(2, '0');
    return `${dd}.${mm}.${yyyy}, ${hh}:${mi} Uhr`;
    }

  // ---- List / Details renderers ----
  private paginate<T>(arr: T[], per: number, maxPages: number): T[][] {
    const pages: T[][] = [];
    for (let i = 0; i < arr.length; i += per) {
      pages.push(arr.slice(i, i + per));
      if (pages.length >= maxPages) break;
    }
    return pages;
  }

  private renderList(root: HTMLElement, pages: SaWiEvent[][], rotateSeconds: number, qrToggleSeconds: number) {
    const grid = root.querySelector('#sawi-grid') as HTMLElement;
    const dots = root.querySelector('#sawi-pager') as HTMLElement;

    let current = 0;
    const draw = (idx: number) => {
      current = idx;
      grid.innerHTML = pages[idx].map(e => this.cardHtml(e)).join('');
      this.afterCardsHydration(grid, qrToggleSeconds);
      // pager
      if (dots) {
        dots.innerHTML = pages.map((_, i) => `<span class="dot ${i === idx ? 'active' : ''}" data-i="${i}"></span>`).join('');
        dots.querySelectorAll('.dot').forEach(d => d.addEventListener('click', () => {
          const i = Number((d as HTMLElement).dataset.i);
          draw(i);
        }));
        dots.style.display = pages.length > 1 ? 'flex' : 'none';
      }
    };

    draw(0);
    if (pages.length > 1 && rotateSeconds > 0) {
      setInterval(() => draw((current + 1) % pages.length), rotateSeconds * 1000);
    }
  }

  private renderDetails(root: HTMLElement, items: SaWiEvent[], qrToggleSeconds: number) {
    const container = root.querySelector('#sawi-details') as HTMLElement;
    container.innerHTML = items.map(e => this.cardHtml(e, true)).join('');
    this.afterCardsHydration(container, qrToggleSeconds);
  }

  // ---- Card HTML + behavior ----
  private statusDot(e: SaWiEvent): { cls: string; text: string } {
    if (e.soldOut || e.ticketsRemaining === 0) return { cls: 'red', text: 'Ausverkauft' };
    if (typeof e.ticketsRemaining === 'number' && e.ticketsRemaining <= 99) return { cls: 'yellow', text: 'Wenige Tickets verfügbar' };
    return { cls: 'green', text: 'Tickets verfügbar' };
  }

  private cardHtml(e: SaWiEvent, big = false): string {
    const st = this.statusDot(e);
    return `
      <article class="sawi-card ${big ? 'big' : ''}">
        <div class="media-wrap">
          <img class="img-layer" src="${e.imageUrl || ''}" alt="${this.escape(e.title)}">
          <div class="qr-layer hidden">
            <canvas class="qr" width="180" height="180" data-url="${this.escape(e.ticketUrl)}"></canvas>
          </div>
        </div>
        <div class="meta">
          <time class="when">${this.escape(e.dateLabel)}</time>
          <h3 class="title">${this.escape(e.title)}</h3>
          <div class="status"><span class="dot ${st.cls}"></span>${st.text}</div>
        </div>
      </article>
    `;
  }

  private escape(s: any): string {
    return String(s ?? '').replace(/[&<>"']/g, m =>
      ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;',"'":'&#39;'} as any)[m]);
  }

  private async afterCardsHydration(scope: HTMLElement, qrToggleSeconds: number) {
    // Lazy-load QR library (only if needed)
    const canvases = Array.from(scope.querySelectorAll('canvas.qr')) as HTMLCanvasElement[];
    const needQr = canvases.filter(c => c.dataset.url).length > 0;
    if (needQr && !(window as any).QRCode) {
      await this.loadScript('https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js');
    }

    // Draw QRs and start image↔QR toggles
    Array.from(scope.querySelectorAll('.sawi-card')).forEach((card: Element) => {
      const canvas = card.querySelector('canvas.qr') as HTMLCanvasElement | null;
      const img = card.querySelector('img.img-layer') as HTMLImageElement | null;
      const qrLayer = card.querySelector('.qr-layer') as HTMLElement | null;

      if (canvas && canvas.dataset.url && (window as any).QRCode) {
        try {
          (window as any).QRCode.toCanvas(canvas, canvas.dataset.url, { width: canvas.width, height: canvas.height, margin: 0 });
        } catch (e) { console.warn('QR draw failed', e); }
      }

      if (img && qrLayer) {
        let on = false;
        setInterval(() => {
          on = !on;
          if (on) { img.classList.add('hidden'); qrLayer.classList.remove('hidden'); }
          else { qrLayer.classList.add('hidden'); img.classList.remove('hidden'); }
        }, Math.max(2, qrToggleSeconds) * 1000);
      }
    });
  }

  private loadScript(src: string): Promise<void> {
    return new Promise((res, rej) => {
      const s = document.createElement('script');
      s.src = src; s.async = true;
      s.onload = () => res();
      s.onerror = (e) => rej(e);
      document.head.appendChild(s);
    });
  }
}
