{
  "compilerOptions": {
    "target": "es2016",
    "module": "esnext",
    "moduleResolution": "nodenext",
		"strictPropertyInitialization": false,
		"noImplicitAny": false,
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
		"experimentalDecorators": true,
		"emitDecoratorMetadata": true,
    "sourceMap": true,
    // Ensure that .d.ts files are created by tsc, but not .js files
    "declaration": true,
    "declarationMap": true,
    "emitDeclarationOnly": true,
    // Ensure that Babel can safely transpile files in the TypeScript project
		"isolatedModules": true,
		"types": ["node"],
    "baseUrl": "./",
    "paths": {
      "*": ["./src/*"]
    },
    "outDir": "./dist/"
  },
  "include": ["src/**/*.ts", "src/index.ts", "src/environment.ts"],
  "exclude": [
    "node_modules",
    "target",
    "build",
    "dist",
    "tmp",
    "src/**/*.spec.ts"
  ]
}
