
.playing {

	& .spot {
		opacity: 1;
		transition: opacity 0.5s ease-in;
	}
}

.spot
{
	height: 100%;
	opacity: 0;
	transition: opacity 0.2s ease-out;

	& .container {
		position: relative;
		height: 100%;
		display: flex;
		flex-direction: column;

		.box-id {
			position: absolute;
			top: 1rem;
			left: .5rem;
		}
	}
	& .imageContainer {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
	}

	& .contentContainer {
		position: absolute;
		right: 0;
		top: 10%;
		padding: 3rem;
		background: rgba(255,255,255,0.7);

		& h1
		{
			font-size: 3rem;
			margin: 2rem 0;
		}


		& td
		{
			font-size: 1.5rem;
			padding: 0.3rem 1rem;
		}
	}
}
