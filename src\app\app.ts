/* component */
import { CORE } from "./core/core.const";
import { SpotComponent } from "./components/spot/spot";
import { IEnvironment } from "./core/types/environment.type";
import { EnvironmentService } from "./core/services/environment.service";
import { LoggerService } from "./core/services/logger.service";
import { GfWizardBaseService } from "./core/services/gf-wizard-base.service";
import { AscDataService } from "./core/services/asc-data.service";
import { GfSpotBaseService } from "./core/services/gf-spot-base.service";
import { AppService } from "./core/services/app.service";
import "../style.scss";

export class App
{
	// Variables
	private _parent: HTMLElement;
	private _spotComponent: SpotComponent;
	private _initCompleteSent: boolean;
	private _playing: boolean;
	private _env: IEnvironment

	constructor(parent: HTMLElement)
	{
		this._parent = parent;
		this._env = EnvironmentService.getEnvironment()

		this._spotComponent = new SpotComponent();

		this._initCompleteSent = false;
		this._playing = false;

		this._init();
	}

	private _init(): void
	{
		LoggerService.debug("App._init: called.");

		let callerType = AppService.getCallerType();
		LoggerService.debug("App._init: caller type: " + callerType);

		if (callerType === CORE.CALLER_TYPE.ASC_EDITOR || (this._env.mock && this._env.mock.ascEditor)) {
			document.body.classList.add(CORE.CLASS_NAME.ASC_EDITOR);
		}

		// !IMPORTANT: register event handler
		GfWizardBaseService.registerDataChangedHandler(this._onDataChanged.bind(this));
		GfWizardBaseService.registerPlayHandler(this.play.bind(this));
		GfWizardBaseService.registerStopHandler(this.stop.bind(this));
		GfWizardBaseService.sendReady(true);
	}

	private _onDataChanged(jsonData: any): void
	{
		LoggerService.info("Received ASC data!");

		// update config & fonts
		AscDataService.init(GfWizardBaseService.getElementData(), jsonData);

		this._render()
			.then(() =>
			{
				if (!this._initCompleteSent)
				{
					GfWizardBaseService.sendInitComplete();
					this._initCompleteSent = true;
				}
			}).catch((error) =>
			{
				LoggerService.error("App._onDataChanged: " + error);
			});
	}

	private _render(): Promise<void>
	{
		LoggerService.debug("App._render: called.");

		return Promise.resolve()
			.then(() => this._spotComponent.render())
			.then((rendered) =>
			{
				this._parent.innerHTML = "";
				this._parent.appendChild(rendered);
			})
			.catch((error) =>
			{
				LoggerService.error("App._render: " + error);
				this.quit();
			});
	}

	play(): void
	{
		LoggerService.debug("App.play: got play.");
		this._playing = true;
		document.body.classList.add(CORE.CLASS_NAME.PLAYING);
	}

	stop(): void
	{
		LoggerService.debug("App.stop: got stop.");
		this._playing = false;
		document.body.classList.remove(CORE.CLASS_NAME.PLAYING);
	}

	quit(): void
	{
		LoggerService.info("App.quit: quitting spot...");
		GfSpotBaseService.quit();
	}
}
